# Prometheus Alert Rules for Backup Exporter
# Sử dụng các metrics mới để tạo alerts dễ dàng hơn

groups:
  - name: backup_alerts
    rules:
      # Alert khi backup quá cũ (sử dụng backup_latest_file_age_seconds)
      - alert: BackupTooOld
        expr: backup_latest_file_age_seconds > 86400  # 24 hours
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Backup file is too old"
          description: "Latest backup file in {{ $labels.path }} is {{ $value }} seconds old ({{ humanizeDuration $value }})"

      # Alert khi backup rất cũ (critical)
      - alert: BackupCriticallyOld
        expr: backup_latest_file_age_seconds > 172800  # 48 hours
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Backup file is critically old"
          description: "Latest backup file in {{ $labels.path }} is {{ $value }} seconds old ({{ humanizeDuration $value }}). Immediate attention required!"

      # Alert khi backup folder không tồn tại
      - alert: BackupFolderMissing
        expr: backup_folder_exists == 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Backup folder does not exist"
          description: "Backup folder {{ $labels.path }} does not exist or is not accessible"

      # Alert khi không có file nào trong backup folder
      - alert: BackupFolderEmpty
        expr: backup_folder_file_count_total == 0 and backup_folder_exists == 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Backup folder is empty"
          description: "Backup folder {{ $labels.path }} exists but contains no files"

      # Alert khi exporter không hoạt động (sử dụng backup_metrics_generated_timestamp_seconds)
      - alert: BackupExporterDown
        expr: time() - backup_metrics_generated_timestamp_seconds > 300  # 5 minutes
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Backup exporter is not responding"
          description: "Backup exporter has not generated metrics for {{ $value }} seconds. Last metrics generated at {{ $labels.backup_metrics_generated_timestamp_seconds | humanizeTimestamp }}"

      # Alert khi backup size quá nhỏ (có thể backup bị lỗi)
      - alert: BackupSizeTooSmall
        expr: backup_folder_size_bytes < 1048576 and backup_folder_exists == 1  # < 1MB
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Backup size is suspiciously small"
          description: "Total backup size in {{ $labels.path }} is only {{ humanizeBytes $value }}, which might indicate a failed backup"

# Ví dụ sử dụng trong Prometheus configuration (prometheus.yml):
#
# rule_files:
#   - "prometheus_alerts.yml"
#
# alerting:
#   alertmanagers:
#     - static_configs:
#         - targets:
#           - alertmanager:9093

# Ví dụ queries hữu ích:
#
# 1. Kiểm tra backup age trong hours:
#    backup_latest_file_age_seconds / 3600
#
# 2. Kiểm tra backup age trong days:
#    backup_latest_file_age_seconds / 86400
#
# 3. Kiểm tra exporter uptime:
#    time() - backup_metrics_generated_timestamp_seconds
#
# 4. Backup size in MB:
#    backup_folder_size_bytes / 1024 / 1024
#
# 5. Alert nếu backup > 7 days:
#    backup_latest_file_age_seconds > 604800
