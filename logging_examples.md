# Logging Examples for Backup Exporter

## Log Levels

### DEBUG
Shows detailed information for debugging:
```bash
export LOG_LEVEL=DEBUG
python backup_exporter.py
```
Output includes:
- Metrics cache refresh decisions
- File scanning details
- HTTP request/response details
- Latest file detection

### INFO (Default)
Shows general operational information:
```bash
export LOG_LEVEL=INFO  # or omit (default)
python backup_exporter.py
```
Output includes:
- Server startup information
- Metrics collection summaries
- HTTP requests from clients

### WARNING
Shows only warnings and errors:
```bash
export LOG_LEVEL=WARNING
python backup_exporter.py
```
Output includes:
- Missing backup folders
- 404 errors
- Configuration issues

### ERROR
Shows only errors:
```bash
export LOG_LEVEL=ERROR
python backup_exporter.py
```
Output includes:
- File system errors
- Server errors
- Exception stack traces

## Custom Log Format

### Default Format
```
2024-01-15 10:30:45 [INFO] - Server started at http://0.0.0.0:8080
```

### Custom Format Examples
```bash
# Simple format
export LOG_FORMAT="%(levelname)s: %(message)s"

# Detailed format with module
export LOG_FORMAT="[%(asctime)s] %(name)s.%(funcName)s:%(lineno)d - %(levelname)s - %(message)s"

# JSON-like format
export LOG_FORMAT='{"time":"%(asctime)s","level":"%(levelname)s","msg":"%(message)s"}'
```

## Docker Logging

### View logs in real-time
```bash
docker-compose logs -f backup-exporter
```

### Set debug level in docker-compose
```yaml
environment:
  - LOG_LEVEL=DEBUG
```

### Collect logs to file
```bash
docker-compose logs backup-exporter > backup-exporter.log
```
