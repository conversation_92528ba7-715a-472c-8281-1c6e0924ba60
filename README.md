# Backup Folder Exporter (Python)

Exporter HTTP đơn giản để monitor backup folders và export metrics cho Prometheus.  
Viết bằng <PERSON> th<PERSON>, khô<PERSON> cần thư việ<PERSON> ngo<PERSON>i, d<PERSON> triển khai với Docker.

## Quick Start

### 1. Chạy trực tiếp (local)
```bash
# Export environment variables
export BACKUP_PATH=/path/to/backup
export PORT=8080

# Chạy exporter
python backup_exporter.py
```
Truy cập:
```bash
curl http://localhost:8080/metrics
```

### 2. Ch<PERSON>y với Docker
```bash
docker build -t backup-exporter .
docker run -d \
  -v /path/to/backup:/backup:ro \
  -e BACKUP_PATH=/backup \
  -e PORT=8080 \
  -p 8080:8080 \
  backup-exporter
```
Dùng docker-compose
```bash
docker-compose up -d
```
## Configuration
<PERSON><PERSON><PERSON> biến môi trường hỗ trợ:

| Variable          | Default | Description                              |
| ----------------- |---------| ---------------------------------------- |
| `BACKUP_PATH`     | /backup | Path tới backup folder (bắt buộc)        |
| `PORT`            | 8080    | HTTP server port                         |
| `HOST`            | 0.0.0.0 | Host để bind                             |
| `UPDATE_INTERVAL` | 60      | Interval (giây) để refresh metrics cache |
| `LOG_LEVEL`       | INFO    | Log level (DEBUG, INFO, WARNING, ERROR) |
| `LOG_FORMAT`      | %(asctime)s - %(name)s - %(levelname)s - %(message)s | Log format string |

## API Endpoints
- `GET /metrics` - Prometheus format metrics
- `GET /health` - Health check (trả về ok)
- `GET /` - Thông tin exporter cơ bản

## Metrics
Các metrics được export:
- `backup_folder_exists{path="/backup"}` - Folder tồn tại (1/0)
- `backup_folder_size_bytes{path="/backup"}` - Tổng dung lượng folder
- `backup_folder_file_count_total{path="/backup"}` - Số lượng file
- `backup_latest_file_timestamp{path="/backup"}` - Timestamp file mới nhất
- `backup_latest_file_size_bytes{path="/backup"}` - Dung lượng file mới nhất

Ví dụ output:
```text
backup_folder_exists{path="/backup"} 1
backup_folder_size_bytes{path="/backup"} 123456789
backup_folder_file_count_total{path="/backup"} 42
backup_latest_file_timestamp{path="/backup"} **********
backup_latest_file_size_bytes{path="/backup"} 2048
```