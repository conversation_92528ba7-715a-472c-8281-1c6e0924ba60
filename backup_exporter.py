#!/usr/bin/env python3
"""
Backup Folder Exporter for Prometheus
Simple HTTP exporter to monitor backup folders and export metrics.
"""

import os
import time
import json
import logging
import threading
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse
from pathlib import Path


class BackupMetrics:
    """Class to collect and cache backup folder metrics"""

    def __init__(self, backup_path, update_interval=60):
        self.backup_path = Path(backup_path)
        self.update_interval = update_interval
        self.metrics_cache = {}
        self.last_update = 0
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        
    def collect_metrics(self):
        """Collect metrics from backup folder"""
        self.logger.debug(f"Collecting metrics for backup path: {self.backup_path}")

        metrics = {
            'backup_folder_exists': 0,
            'backup_folder_size_bytes': 0,
            'backup_folder_file_count_total': 0,
            'backup_latest_file_timestamp': 0,
            'backup_latest_file_size_bytes': 0
        }

        try:
            if self.backup_path.exists() and self.backup_path.is_dir():
                self.logger.debug(f"Backup folder exists: {self.backup_path}")
                metrics['backup_folder_exists'] = 1

                # Calculate folder size and file count
                total_size = 0
                file_count = 0
                latest_file = None
                latest_timestamp = 0

                for file_path in self.backup_path.rglob('*'):
                    if file_path.is_file():
                        file_count += 1
                        file_size = file_path.stat().st_size
                        total_size += file_size
                        file_mtime = file_path.stat().st_mtime

                        if file_mtime > latest_timestamp:
                            latest_timestamp = file_mtime
                            latest_file = file_path
                            metrics['backup_latest_file_size_bytes'] = file_size

                metrics['backup_folder_size_bytes'] = total_size
                metrics['backup_folder_file_count_total'] = file_count
                metrics['backup_latest_file_timestamp'] = int(latest_timestamp)

                self.logger.info(f"Metrics collected: {file_count} files, {total_size} bytes total")
                if latest_file:
                    self.logger.debug(f"Latest file: {latest_file} ({latest_timestamp})")
            else:
                self.logger.warning(f"Backup folder does not exist or is not a directory: {self.backup_path}")

        except Exception as e:
            self.logger.error(f"Error collecting metrics: {e}", exc_info=True)

        return metrics
    
    def get_metrics(self):
        """Get cached metrics, refresh if needed"""
        current_time = time.time()

        with self.lock:
            if current_time - self.last_update > self.update_interval:
                self.logger.debug("Refreshing metrics cache")
                self.metrics_cache = self.collect_metrics()
                self.last_update = current_time
            else:
                self.logger.debug("Using cached metrics")

        return self.metrics_cache.copy()
    
    def format_prometheus_metrics(self):
        """Format metrics in Prometheus format"""
        metrics = self.get_metrics()
        path_label = str(self.backup_path)
        
        lines = []
        for metric_name, value in metrics.items():
            lines.append(f'{metric_name}{{path="{path_label}"}} {value}')
        
        return '\n'.join(lines) + '\n'


class BackupExporterHandler(BaseHTTPRequestHandler):
    """HTTP request handler for backup exporter"""

    def __init__(self, metrics_collector, *args, **kwargs):
        self.metrics_collector = metrics_collector
        self.logger = logging.getLogger(__name__)
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path

        self.logger.info(f"GET request: {path} from {self.client_address[0]}")

        if path == '/metrics':
            self.handle_metrics()
        elif path == '/health':
            self.handle_health()
        elif path == '/':
            self.handle_info()
        else:
            self.logger.warning(f"404 Not Found: {path}")
            self.send_error(404, "Not Found")
    
    def handle_metrics(self):
        """Handle /metrics endpoint"""
        try:
            self.logger.debug("Generating metrics response")
            metrics_output = self.metrics_collector.format_prometheus_metrics()
            self.send_response(200)
            self.send_header('Content-Type', 'text/plain; charset=utf-8')
            self.end_headers()
            self.wfile.write(metrics_output.encode('utf-8'))
            self.logger.debug("Metrics response sent successfully")
        except Exception as e:
            self.logger.error(f"Error handling metrics request: {e}", exc_info=True)
            self.send_error(500, f"Internal Server Error: {e}")
    
    def handle_health(self):
        """Handle /health endpoint"""
        self.logger.debug("Health check requested")
        self.send_response(200)
        self.send_header('Content-Type', 'text/plain')
        self.end_headers()
        self.wfile.write(b'ok')
    
    def handle_info(self):
        """Handle / endpoint with basic info"""
        self.logger.debug("Info endpoint requested")
        info = {
            'name': 'Backup Folder Exporter',
            'version': '1.0.0',
            'backup_path': str(self.metrics_collector.backup_path),
            'endpoints': ['/metrics', '/health', '/']
        }

        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(info, indent=2).encode('utf-8'))
    
    def log_message(self, format, *args):
        """Override to use proper logging instead of stderr"""
        self.logger.info(f"{self.address_string()} - {format % args}")


def create_handler(metrics_collector):
    """Create handler with metrics collector"""
    def handler(*args, **kwargs):
        return BackupExporterHandler(metrics_collector, *args, **kwargs)
    return handler


def setup_logging():
    """Setup logging configuration"""
    log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
    log_format = os.getenv('LOG_FORMAT', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    logging.basicConfig(
        level=getattr(logging, log_level, logging.INFO),
        format=log_format,
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # Reduce noise from HTTP server if not in DEBUG mode
    if log_level != 'DEBUG':
        logging.getLogger('http.server').setLevel(logging.WARNING)


def main():
    """Main function"""
    # Setup logging first
    setup_logging()
    logger = logging.getLogger(__name__)

    # Get configuration from environment variables
    backup_path = os.getenv('BACKUP_PATH', 'backup')
    port = int(os.getenv('PORT', 8080))
    host = os.getenv('HOST', '0.0.0.0')
    update_interval = int(os.getenv('UPDATE_INTERVAL', 60))

    logger.info("Starting Backup Exporter...")
    logger.info(f"Backup Path: {backup_path}")
    logger.info(f"Server: {host}:{port}")
    logger.info(f"Update Interval: {update_interval}s")
    logger.info(f"Log Level: {logging.getLogger().level}")

    # Initialize metrics collector
    metrics_collector = BackupMetrics(backup_path, update_interval)

    # Create HTTP server
    handler = create_handler(metrics_collector)
    server = HTTPServer((host, port), handler)

    logger.info(f"Server started at http://{host}:{port}")
    logger.info("Endpoints:")
    logger.info("  GET /metrics - Prometheus metrics")
    logger.info("  GET /health  - Health check")
    logger.info("  GET /        - Basic info")

    try:
        server.serve_forever()
    except KeyboardInterrupt:
        logger.info("Received interrupt signal, shutting down server...")
        server.shutdown()
        logger.info("Server shutdown complete")


if __name__ == '__main__':
    main()
