#!/usr/bin/env python3
"""
Backup Folder Exporter for Prometheus
Simple HTTP exporter to monitor backup folders and export metrics.
"""

import os
import time
import json
import threading
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse
from pathlib import Path


class BackupMetrics:
    """Class to collect and cache backup folder metrics"""
    
    def __init__(self, backup_path, update_interval=60):
        self.backup_path = Path(backup_path)
        self.update_interval = update_interval
        self.metrics_cache = {}
        self.last_update = 0
        self.lock = threading.Lock()
        
    def collect_metrics(self):
        """Collect metrics from backup folder"""
        metrics = {
            'backup_folder_exists': 0,
            'backup_folder_size_bytes': 0,
            'backup_folder_file_count_total': 0,
            'backup_latest_file_timestamp': 0,
            'backup_latest_file_size_bytes': 0
        }
        
        try:
            if self.backup_path.exists() and self.backup_path.is_dir():
                metrics['backup_folder_exists'] = 1
                
                # Calculate folder size and file count
                total_size = 0
                file_count = 0
                latest_file = None
                latest_timestamp = 0
                
                for file_path in self.backup_path.rglob('*'):
                    if file_path.is_file():
                        file_count += 1
                        file_size = file_path.stat().st_size
                        total_size += file_size
                        file_mtime = file_path.stat().st_mtime
                        
                        if file_mtime > latest_timestamp:
                            latest_timestamp = file_mtime
                            latest_file = file_path
                            metrics['backup_latest_file_size_bytes'] = file_size
                
                metrics['backup_folder_size_bytes'] = total_size
                metrics['backup_folder_file_count_total'] = file_count
                metrics['backup_latest_file_timestamp'] = int(latest_timestamp)
                
        except Exception as e:
            print(f"Error collecting metrics: {e}")
            
        return metrics
    
    def get_metrics(self):
        """Get cached metrics, refresh if needed"""
        current_time = time.time()
        
        with self.lock:
            if current_time - self.last_update > self.update_interval:
                self.metrics_cache = self.collect_metrics()
                self.last_update = current_time
                
        return self.metrics_cache.copy()
    
    def format_prometheus_metrics(self):
        """Format metrics in Prometheus format"""
        metrics = self.get_metrics()
        path_label = str(self.backup_path)
        
        lines = []
        for metric_name, value in metrics.items():
            lines.append(f'{metric_name}{{path="{path_label}"}} {value}')
        
        return '\n'.join(lines) + '\n'


class BackupExporterHandler(BaseHTTPRequestHandler):
    """HTTP request handler for backup exporter"""
    
    def __init__(self, metrics_collector, *args, **kwargs):
        self.metrics_collector = metrics_collector
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        if path == '/metrics':
            self.handle_metrics()
        elif path == '/health':
            self.handle_health()
        elif path == '/':
            self.handle_info()
        else:
            self.send_error(404, "Not Found")
    
    def handle_metrics(self):
        """Handle /metrics endpoint"""
        try:
            metrics_output = self.metrics_collector.format_prometheus_metrics()
            self.send_response(200)
            self.send_header('Content-Type', 'text/plain; charset=utf-8')
            self.end_headers()
            self.wfile.write(metrics_output.encode('utf-8'))
        except Exception as e:
            self.send_error(500, f"Internal Server Error: {e}")
    
    def handle_health(self):
        """Handle /health endpoint"""
        self.send_response(200)
        self.send_header('Content-Type', 'text/plain')
        self.end_headers()
        self.wfile.write(b'ok')
    
    def handle_info(self):
        """Handle / endpoint with basic info"""
        info = {
            'name': 'Backup Folder Exporter',
            'version': '1.0.0',
            'backup_path': str(self.metrics_collector.backup_path),
            'endpoints': ['/metrics', '/health', '/']
        }
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(info, indent=2).encode('utf-8'))
    
    def log_message(self, format, *args):
        """Override to customize logging"""
        print(f"{self.address_string()} - {format % args}")


def create_handler(metrics_collector):
    """Create handler with metrics collector"""
    def handler(*args, **kwargs):
        return BackupExporterHandler(metrics_collector, *args, **kwargs)
    return handler


def main():
    """Main function"""
    # Get configuration from environment variables
    backup_path = os.getenv('BACKUP_PATH', 'backup')
    port = int(os.getenv('PORT', 8080))
    host = os.getenv('HOST', '0.0.0.0')
    update_interval = int(os.getenv('UPDATE_INTERVAL', 60))
    
    print(f"Starting Backup Exporter...")
    print(f"Backup Path: {backup_path}")
    print(f"Server: {host}:{port}")
    print(f"Update Interval: {update_interval}s")
    
    # Initialize metrics collector
    metrics_collector = BackupMetrics(backup_path, update_interval)
    
    # Create HTTP server
    handler = create_handler(metrics_collector)
    server = HTTPServer((host, port), handler)
    
    print(f"Server started at http://{host}:{port}")
    print("Endpoints:")
    print("  GET /metrics - Prometheus metrics")
    print("  GET /health  - Health check")
    print("  GET /        - Basic info")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\nShutting down server...")
        server.shutdown()


if __name__ == '__main__':
    main()
