version: '3.8'

services:
  backup-exporter:
    build: .
    container_name: backup-exporter
    ports:
      - "8080:8080"
    volumes:
      # Mount your backup directory here (read-only)
      # Change /path/to/your/backup to your actual backup path
      - /path/to/your/backup:/backup:ro
    environment:
      - BACKUP_PATH=/backup
      - PORT=8080
      - HOST=0.0.0.0
      - UPDATE_INTERVAL=60
      - LOG_LEVEL=INFO
      - LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8080/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

# Example configuration for development/testing
# Uncomment and modify as needed
#
# services:
#   backup-exporter-dev:
#     build: .
#     container_name: backup-exporter-dev
#     ports:
#       - "8080:8080"
#     volumes:
#       # Example: mount a test backup directory
#       - ./test-backup:/backup:ro
#     environment:
#       - BACKUP_PATH=/backup
#       - PORT=8080
#       - HOST=0.0.0.0
#       - UPDATE_INTERVAL=30  # More frequent updates for testing
#     restart: unless-stopped
