#!/usr/bin/env python3
"""
Test script to verify backup exporter metrics
"""

import os
import time
import tempfile
import requests
from pathlib import Path


def create_test_backup_folder():
    """Create a temporary backup folder with test files"""
    temp_dir = tempfile.mkdtemp(prefix="backup_test_")
    backup_path = Path(temp_dir)
    
    # Create some test files with different timestamps
    (backup_path / "old_backup.txt").write_text("Old backup content")
    
    # Create a newer file
    time.sleep(1)
    (backup_path / "recent_backup.txt").write_text("Recent backup content")
    
    # Create an even newer file
    time.sleep(1)
    newest_file = backup_path / "newest_backup.txt"
    newest_file.write_text("Newest backup content")
    
    print(f"Created test backup folder: {backup_path}")
    print(f"Files created:")
    for file in backup_path.iterdir():
        stat = file.stat()
        print(f"  {file.name}: {stat.st_size} bytes, modified: {time.ctime(stat.st_mtime)}")
    
    return str(backup_path)


def test_metrics_endpoint(port=8080):
    """Test the /metrics endpoint and verify new metrics"""
    url = f"http://localhost:{port}/metrics"
    
    try:
        response = requests.get(url, timeout=5)
        response.raise_for_status()
        
        metrics_text = response.text
        print("Metrics response:")
        print("=" * 50)
        print(metrics_text)
        print("=" * 50)
        
        # Check for new metrics
        required_metrics = [
            'backup_latest_file_age_seconds',
            'backup_metrics_generated_timestamp_seconds'
        ]
        
        for metric in required_metrics:
            if metric in metrics_text:
                print(f"✅ Found metric: {metric}")
            else:
                print(f"❌ Missing metric: {metric}")
        
        # Parse and display age in human readable format
        for line in metrics_text.split('\n'):
            if 'backup_latest_file_age_seconds' in line:
                try:
                    age_seconds = int(line.split()[-1])
                    age_minutes = age_seconds / 60
                    age_hours = age_seconds / 3600
                    print(f"📅 Latest file age: {age_seconds}s ({age_minutes:.1f}m, {age_hours:.2f}h)")
                except:
                    pass
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Error connecting to metrics endpoint: {e}")
        return False


def main():
    """Main test function"""
    print("🧪 Testing Backup Exporter Metrics")
    print("=" * 40)
    
    # Create test backup folder
    test_backup_path = create_test_backup_folder()
    
    print(f"\n📁 Test backup folder created at: {test_backup_path}")
    print("💡 To test the exporter:")
    print(f"   export BACKUP_PATH={test_backup_path}")
    print("   python backup_exporter.py")
    print("\n🔍 Then run this test script again to verify metrics")
    
    # Try to test metrics if exporter is running
    print("\n🌐 Testing metrics endpoint...")
    if test_metrics_endpoint():
        print("✅ Metrics endpoint test passed!")
    else:
        print("ℹ️  Start the backup exporter first, then run this test again")
    
    print(f"\n🧹 To cleanup test folder: rm -rf {test_backup_path}")


if __name__ == '__main__':
    main()
